// 配置相关类型定义

// 配置基础接口
export interface Config {
  id: number;
  name: string;
  value: any; // JSON 类型，具体结构根据配置名称而定
  createdAt: string;
  updatedAt: string;
}

// 前端盒型配置
export interface FrontBoxConfig {
  boxIds: number[]; // 盒型ID数组，按显示顺序排列
}

// 前端盒型配置参数
export interface FrontBoxConfigParams {
  boxIds: number[];
}

// 获取前端盒型配置的响应
export interface GetFrontBoxConfigResponse {
  config: FrontBoxConfig | null;
}

// 更新前端盒型配置的参数
export interface UpdateFrontBoxConfigParams {
  boxIds: number[];
}

// 获取前端盒型数据的响应
export interface GetFrontBoxResponse {
  boxes: Array<{
    id: number;
    name: string;
    status: number;
    description?: string | null;
    images?: Array<{
      id?: number;
      name: string;
      mimeType?: string;
      sortOrder?: number;
    }>;
  }>;
}

// 配置列表查询参数
export interface ConfigListParams {
  page?: number;
  pageSize?: number;
  name?: string;
}

// 创建配置参数
export interface CreateConfigParams {
  name: string;
  value: any;
}

// 更新配置参数
export interface UpdateConfigParams {
  id: number;
  name?: string;
  value?: any;
}

// 配置详情查询参数
export interface GetConfigDetailParams {
  id?: number;
  name?: string;
}
