
'use client';

import React from 'react';
import { Card, Typography } from 'antd';

const { Text } = Typography;


interface CustomCardProps {
    title?: string;
    imageUrl?: string;
    imageAlt?: string;
    subtitle?: string;
    onClick?: () => void;
    className?: string;
    width?: number | string;
    height?: number | string;
    imageHeight?: number;
    hoverable?: boolean;
}

export default function CustomCard({
    title,
    imageUrl,
    imageAlt = '产品图片',
    subtitle,
    onClick,
    className = '',
    width = 240,
    height = 'auto',
    imageHeight = 160,
    hoverable = true,
}: CustomCardProps) {

    return (
        <div style={{ display: 'flex', justifyContent: 'center', margin: '0 auto', width: '100%' }}>
            <Card
                hoverable={hoverable}
                className={`custom-card ${className}`}
                style={{
                    width,
                    height,
                    cursor: onClick ? 'pointer' : 'default',
                    borderRadius: '8px',
                    overflow: 'hidden',
                }}
                cover={
                    <div
                        style={{
                            height: imageHeight,
                            overflow: 'hidden',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f5f5f5',
                        }}
                    >
                        <img
                            src={imageUrl}
                            alt={imageAlt}
                            style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                                transition: 'transform 0.3s ease',
                            }}
                            onError={(e) => {
                                // 图片加载失败时显示占位符
                                const target = e.target as HTMLImageElement;
                                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQwIiBoZWlnaHQ9IjE2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTk5Ij7ova/mkq3lm748L3RleHQ+PC9zdmc+';
                            }}
                        />
                    </div>
                }
                onClick={onClick}
            >
                <div style={{ textAlign: 'center', padding: '8px 0' }}>
                    <Text
                        strong
                        style={{
                            fontSize: '16px',
                            color: '#333',
                            display: 'block',
                            marginBottom: subtitle ? '4px' : '0',
                        }}
                    >
                        {title}
                    </Text>
                    {subtitle && (
                        <Text
                            type="secondary"
                            style={{
                                fontSize: '14px',
                                color: '#666',
                            }}
                        >
                            {subtitle}
                        </Text>
                    )}
                </div>
            </Card>
        </div>
    );
};
