import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, canAccessAdmin, getTokenFromRequest } from '@/lib/auth/jwt';
import { UserRole } from '@/types/user';

// 需要管理员权限的路径模式
const ADMIN_PATHS = [
  '/admin',
  '/admin/',
  '/admin/dashboard',
  '/admin/box',
  '/admin/customFormula',
  '/admin/material',
  '/admin/craftSalary',
  '/admin/settings',
  '/admin/users'
];

// 不需要权限检查的路径（API路径已有自己的权限中间件）
const EXCLUDED_PATHS = [
  '/api/',
  '/login',
  '/register',
  '/_next/',
  '/favicon.ico',
  '/static/',
  '/images/',
  '/public/'
];

/**
 * 检查路径是否需要管理员权限
 */
function requiresAdminAccess(pathname: string): boolean {
  // 排除不需要检查的路径
  if (EXCLUDED_PATHS.some(path => pathname.startsWith(path))) {
    return false;
  }

  // 检查是否是管理后台路径
  return ADMIN_PATHS.some(path => {
    if (path === '/admin' || path === '/admin/') {
      return pathname === path || pathname.startsWith('/admin/');
    }
    return pathname === path || pathname.startsWith(path + '/');
  });
}



/**
 * 创建重定向响应
 */
function createRedirectResponse(url: string, request: NextRequest): NextResponse {
  const redirectUrl = new URL(url, request.url);
  return NextResponse.redirect(redirectUrl);
}

/**
 * 创建403权限不足页面响应
 */
function createAccessDeniedResponse(request: NextRequest): NextResponse {
  const accessDeniedUrl = `/403?redirect=${encodeURIComponent(request.nextUrl.pathname)}`;
  return createRedirectResponse(accessDeniedUrl, request);
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 检查是否需要管理员权限
  if (!requiresAdminAccess(pathname)) {
    return NextResponse.next();
  }

  // 获取JWT token
  const token = getTokenFromRequest(request);
  
  // 如果没有token，重定向到登录页面
  if (!token) {
    const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;
    return createRedirectResponse(loginUrl, request);
  }

  // 验证token
  const payload = verifyToken(token);
  if (!payload) {
    // Token无效，重定向到登录页面
    const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;
    return createRedirectResponse(loginUrl, request);
  }

  // 检查token是否过期
  if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
    // Token过期，重定向到登录页面
    const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`;
    return createRedirectResponse(loginUrl, request);
  }

  // 检查用户是否有管理后台访问权限
  if (!canAccessAdmin(payload.role)) {
    // 权限不足，返回403页面
    return createAccessDeniedResponse(request);
  }

  // 权限验证通过，继续处理请求
  return NextResponse.next();
}

// 配置middleware匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了：
     * - api路径 (API路径有自己的权限中间件)
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - 其他静态资源
     */
    '/((?!api|_next/static|_next/image|favicon.ico|static|images|public).*)',
  ],
};
