'use client';

import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Button, List, Typography, Alert, Space, Tag, Spin } from 'antd';
import {
  InboxOutlined,
  FileOutlined,
  CheckCircleOutlined,
  UserOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAuth } from '@/hooks/useAuth';
import { getUserRoleLabel, getUserRoleColor } from '@/lib/auth/client';
import FrontBoxConfig from '@/components/admin/FrontBoxConfig';
import { dashboardApi } from '@/services/adminApi';
import { DashboardStats } from '@/types/dashboard';
import { useErrorHandler, useAsyncError } from '@/lib/hooks/useErrorHandler';

const { Title } = Typography;

export default function DashboardPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  // 使用新的错误处理Hook
  const { errorState, clearError } = useErrorHandler();
  const { execute, loading: asyncLoading } = useAsyncError();

  // 获取统计数据
  const fetchStats = async () => {
    const result = await execute(async () => {
      return await dashboardApi.getStats();
    }, '获取统计数据');

    if (result) {
      setStats(result);
    }
    setLoading(false);
  };

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true);
    clearError();
    fetchStats();
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>仪表盘</Title>
        <Button
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={loading || asyncLoading}
        >
          刷新数据
        </Button>
      </div>

      {/* 欢迎信息 */}
      {user && (
        <Alert
          message={
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <span>欢迎访问管理后台，{user.name}！</span>
            </Space>
          }
          description={
            <Space direction="vertical" size="small">
              <div>
                <UserOutlined style={{ marginRight: 8 }} />
                当前角色：
                <Tag color={getUserRoleColor(user.role)} style={{ marginLeft: 8 }}>
                  {getUserRoleLabel(user.role)}
                </Tag>
              </div>
              <div>您拥有管理后台的完整访问权限，可以管理系统的各项功能。</div>
            </Space>
          }
          type="success"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 错误状态显示 */}
      {errorState.hasError && (
        <Alert
          message="数据加载失败"
          description={errorState.error?.message || '获取统计数据时发生错误'}
          type="error"
          showIcon
          closable
          onClose={clearError}
          style={{ marginBottom: 24 }}
          action={
            <Button size="small" onClick={handleRefresh}>
              重试
            </Button>
          }
        />
      )}

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="盒型总数"
              value={stats?.totalBoxes ?? 0}
              prefix={<InboxOutlined />}
              loading={loading || asyncLoading}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已发布盒型"
              value={stats?.publishedBoxes ?? 0}
              prefix={<FileOutlined />}
              loading={loading || asyncLoading}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="草稿盒型"
              value={stats?.draftBoxes ?? 0}
              prefix={<FileOutlined />}
              loading={loading || asyncLoading}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="用户数量"
              value={stats?.totalUsers ?? 0}
              prefix={<UserOutlined />}
              loading={loading || asyncLoading}
            />
          </Card>
        </Col>
      </Row>
      
      {/* <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} md={12}>
          <FrontBoxConfig compact />
        </Col>

        <Col xs={24} md={12}>
          <Card title="快速操作">
            <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
              <Button type="primary" size="middle">新建盒型</Button>
              <Button>导入盒型</Button>
              <Button>系统设置</Button>
            </div>
          </Card>
        </Col>
      </Row> */}
    </div>
  );
} 